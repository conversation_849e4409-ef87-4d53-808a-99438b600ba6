import React, { useState } from 'react';
import { View, Image, TouchableOpacity, Text, StyleSheet, Alert, Platform } from 'react-native';
import * as ImagePickerExpo from 'expo-image-picker';
import { Controller } from 'react-hook-form';
import { Colors } from '../../constants/colors';

interface ImagePickerProps {
  control: any;
  name: string;
  required?: boolean;
  label?: string;
}

export default function ImagePicker({ 
  control, 
  name, 
  required = false,
  label = 'Receipt'
}: ImagePickerProps) {
  const [imageUri, setImageUri] = useState<string | null>(null);

  const pickImage = async (onChange: (uri: string) => void) => {
    try {
      // Request permission first
      const permissionResult = await ImagePickerExpo.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert(
          'Permission Required',
          'Permission to access photo library is required to select images.',
          [{ text: 'OK' }]
        );
        return;
      }

      const result = await ImagePickerExpo.launchImageLibraryAsync({
        mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false, // Reduce data size
        base64: false, // Don't include base64
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        if (asset.uri) {
          setImageUri(asset.uri);
          onChange(asset.uri);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(
        'Error',
        'Failed to pick image. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const takePhoto = async (onChange: (uri: string) => void) => {
    try {
      // Check if camera is available
      const cameraAvailable = await ImagePickerExpo.getCameraPermissionsAsync();

      // Request permission
      const permissionResult = await ImagePickerExpo.requestCameraPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert(
          'Permission Required',
          'Permission to access camera is required to take photos.',
          [{ text: 'OK' }]
        );
        return;
      }

      const result = await ImagePickerExpo.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false, // Reduce data size
        base64: false, // Don't include base64
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        if (asset.uri) {
          setImageUri(asset.uri);
          onChange(asset.uri);
        }
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert(
        'Error',
        'Failed to take photo. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const showImageOptions = async (onChange: (uri: string) => void) => {
    try {
      // Check available options
      const cameraPermissions = await ImagePickerExpo.getCameraPermissionsAsync();
      const mediaPermissions = await ImagePickerExpo.getMediaLibraryPermissionsAsync();

      const options = [];

      // Add camera option if available (not in simulator usually)
      if (Platform.OS !== 'web') {
        options.push({ text: 'Camera', onPress: () => takePhoto(onChange) });
      }

      // Always add gallery option
      options.push({ text: 'Gallery', onPress: () => pickImage(onChange) });
      options.push({ text: 'Cancel', style: 'cancel' as const });

      Alert.alert(
        'Select Image',
        'Choose how you want to add the receipt image',
        options
      );
    } catch (error) {
      console.error('Error showing image options:', error);
      // Fallback to simple options
      Alert.alert(
        'Select Image',
        'Choose how you want to add the receipt image',
        [
          { text: 'Camera', onPress: () => takePhoto(onChange) },
          { text: 'Gallery', onPress: () => pickImage(onChange) },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    }
  };

  return (
    <Controller
      control={control}
      name={name}
      rules={{ required: required ? 'Receipt is required' : false }}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <View style={styles.container}>
          <Text style={styles.label}>
            {label}
            {required && <Text style={styles.required}> *</Text>}
          </Text>
          
          <TouchableOpacity 
            style={[
              styles.picker,
              error && styles.pickerError,
            ]}
            onPress={() => showImageOptions(onChange)}
          >
            {(imageUri || value) ? (
              <Image 
                source={{ uri: imageUri || value }} 
                style={styles.image} 
                resizeMode="cover"
              />
            ) : (
              <View style={styles.placeholder}>
                <Text style={styles.placeholderText}>📷</Text>
                <Text style={styles.placeholderSubtext}>Tap to add receipt</Text>
              </View>
            )}
          </TouchableOpacity>
          
          {error && <Text style={styles.errorText}>{error.message}</Text>}
        </View>
      )}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 8,
  },
  required: {
    color: Colors.error,
  },
  picker: {
    height: 200,
    borderWidth: 2,
    borderColor: Colors.gray300,
    borderStyle: 'dashed',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.gray50,
  },
  pickerError: {
    borderColor: Colors.error,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 6,
  },
  placeholder: {
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 32,
    marginBottom: 8,
  },
  placeholderSubtext: {
    color: Colors.gray500,
    fontSize: 16,
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    marginTop: 4,
  },
});
